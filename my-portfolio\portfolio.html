<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Rohith Avula - Full-Stack Developer & AI Engineer specializing in MERN Stack, Machine Learning, and Intelligent Web Applications. Available for hire.">
    <meta name="keywords" content="Full-Stack Developer, AI Engineer, MERN Stack, React, Node.js, Machine Learning, Web Development, Rohith Avula">
    <meta name="author" content="Rohith Avula">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Rohith Avula | Full-Stack Developer & AI Engineer">
    <meta property="og:description" content="Full-Stack Developer specializing in MERN Stack and AI integration. Building intelligent, scalable web applications.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://rohithavula.dev">
    <meta property="og:image" content="image/profile.jpg.png">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Rohith Avula | Full-Stack Developer & AI Engineer">
    <meta name="twitter:description" content="Full-Stack Developer specializing in MERN Stack and AI integration.">
    <meta name="twitter:image" content="image/profile.jpg.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👨‍💻</text></svg>">

    <title>Rohith Avula | Full-Stack Developer & AI Engineer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #0ea5e9;
            --dark: #0f172a;
            --dark-light: #1e293b;
            --gray: #64748b;
            --light: #f1f5f9;
            --white: #ffffff;
            --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: var(--white);
            overflow-x: hidden;
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            background: var(--gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            gap: 5px;
        }

        .menu-toggle span {
            width: 25px;
            height: 3px;
            background: var(--dark);
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(8px, 8px);
        }

        .menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -7px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
            padding: 7rem 1.5rem 4rem;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .hero-content .highlight {
            background: var(--gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content .subtitle {
            font-size: 1.2rem;
            color: var(--gray);
            margin-bottom: 1.5rem;
            font-weight: 400;
        }

        .hero-description {
            font-size: 1.05rem;
            color: var(--gray);
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.9rem 1.8rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            border: none;
            cursor: pointer;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: var(--gradient);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        }

        .btn-secondary {
            background: var(--white);
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn-secondary:hover {
            background: var(--primary);
            color: var(--white);
        }

        .hero-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .profile-wrapper {
            position: relative;
            width: 380px;
            height: 380px;
        }

        /* Professional Illustrated Avatar */
        .avatar-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .avatar-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            background: var(--gradient);
            border-radius: 50%;
            padding: 8px;
        }

        .avatar-content {
            width: 100%;
            height: 100%;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* Modern Developer Avatar SVG Style */
        .developer-avatar {
            width: 85%;
            height: 85%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .avatar-head {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            position: relative;
            margin-bottom: 10px;
        }

        .avatar-face {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-eyes {
            position: absolute;
            top: 45px;
            display: flex;
            gap: 25px;
        }

        .eye {
            width: 12px;
            height: 12px;
            background: var(--white);
            border-radius: 50%;
            position: relative;
        }

        .eye::after {
            content: '';
            width: 6px;
            height: 6px;
            background: var(--dark);
            border-radius: 50%;
            position: absolute;
            top: 3px;
            left: 3px;
        }

        .avatar-smile {
            position: absolute;
            top: 65px;
            width: 30px;
            height: 15px;
            border: 3px solid var(--white);
            border-top: none;
            border-radius: 0 0 30px 30px;
        }

        .avatar-body {
            width: 140px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50px 50px 20px 20px;
            position: relative;
        }

        .avatar-code {
            position: absolute;
            top: 20px;
            left: 20px;
            color: var(--white);
            font-size: 12px;
            font-family: monospace;
            opacity: 0.7;
        }

        /* Profile Photo Styles */
        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid var(--white);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .profile-photo:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        /* Avatar Fallback */
        .avatar-fallback {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            font-weight: bold;
            color: var(--white);
            background: var(--gradient);
            border-radius: 50%;
        }

        /* Form Validation Styles */
        .form-group.error input,
        .form-group.error textarea {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.05);
        }

        .form-group.success input,
        .form-group.success textarea {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.05);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.3rem;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        /* Loading states */
        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        /* Skip Link for Accessibility */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary);
            color: var(--white);
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 10000;
            font-weight: 600;
            transition: top 0.3s;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .profile-wrapper {
                animation: none;
            }

            html {
                scroll-behavior: auto;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --primary: #0000ff;
                --secondary: #008000;
                --dark: #000000;
                --gray: #333333;
            }
        }

        /* Print styles */
        @media print {
            .loading-screen,
            .scroll-top,
            nav,
            .contact-form {
                display: none !important;
            }

            body {
                font-size: 12pt;
                line-height: 1.4;
            }

            .hero,
            .about,
            .projects {
                page-break-inside: avoid;
            }
        }

        /* About Section */
        .about {
            padding: 5rem 1.5rem;
            background: var(--white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.8rem;
            color: var(--dark);
        }

        .section-subtitle {
            text-align: center;
            color: var(--gray);
            font-size: 1.05rem;
            margin-bottom: 3rem;
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: start;
        }

        .about-text p {
            color: var(--gray);
            font-size: 1.05rem;
            line-height: 1.8;
            margin-bottom: 1.3rem;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.2rem;
        }

        .skill-category {
            background: var(--light);
            padding: 1.3rem;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .skill-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .skill-category h3 {
            font-size: 1rem;
            margin-bottom: 0.8rem;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .skill-category .icon {
            font-size: 1.3rem;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background: var(--white);
            color: var(--gray);
            padding: 0.35rem 0.9rem;
            border-radius: 20px;
            font-size: 0.8rem;
            border: 1px solid #e2e8f0;
        }

        /* Projects Section */
        .projects {
            padding: 5rem 1.5rem;
            background: var(--light);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 2.5rem;
        }

        .project-card {
            background: var(--white);
            border-radius: 18px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .project-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .project-image {
            height: 180px;
            background: var(--gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3.5rem;
            color: var(--white);
        }

        .project-content {
            padding: 1.8rem;
        }

        .project-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .project-description {
            color: var(--gray);
            margin-bottom: 1.2rem;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.2rem;
        }

        .tech-tag {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
            padding: 0.25rem 0.7rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .project-links {
            display: flex;
            gap: 1rem;
        }

        .project-link {
            text-decoration: none;
            color: var(--primary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .project-link:hover {
            color: var(--primary-dark);
            gap: 0.7rem;
        }

        /* Contact Section */
        .contact {
            padding: 5rem 1.5rem;
            background: var(--dark);
            color: var(--white);
        }

        .contact .section-title,
        .contact .section-subtitle {
            color: var(--white);
        }

        .contact .section-subtitle {
            opacity: 0.8;
        }

        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-top: 2.5rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1.2rem;
            padding: 1.3rem;
            background: var(--dark-light);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateX(8px);
            background: rgba(99, 102, 241, 0.1);
        }

        .contact-icon {
            width: 45px;
            height: 45px;
            background: var(--gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            flex-shrink: 0;
        }

        .contact-details h3 {
            font-size: 1rem;
            margin-bottom: 0.2rem;
        }

        .contact-details p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .contact-details a {
            color: var(--white);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-details a:hover {
            color: var(--primary);
        }

        .contact-form {
            background: var(--dark-light);
            padding: 1.8rem;
            border-radius: 18px;
        }

        .form-group {
            margin-bottom: 1.3rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.9rem;
            border: 2px solid rgba(255,255,255,0.1);
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
            color: var(--white);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            background: rgba(255,255,255,0.08);
        }

        .form-group textarea {
            min-height: 130px;
            resize: vertical;
        }

        /* Footer */
        footer {
            background: var(--dark);
            padding: 2rem 1.5rem;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1.2rem;
            margin-bottom: 1.2rem;
        }

        .social-link {
            width: 42px;
            height: 42px;
            background: var(--dark-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--gradient);
            transform: translateY(-3px);
        }

        footer p {
            color: rgba(255,255,255,0.6);
            font-size: 0.85rem;
        }

        /* Mobile Responsive */
        @media (max-width: 968px) {
            .nav-links {
                position: fixed;
                top: 70px;
                right: -100%;
                width: 70%;
                max-width: 300px;
                height: calc(100vh - 70px);
                background: var(--white);
                flex-direction: column;
                padding: 2rem;
                box-shadow: -5px 0 20px rgba(0,0,0,0.1);
                transition: right 0.3s ease;
                gap: 0;
            }

            .nav-links.active {
                right: 0;
            }

            .nav-links li {
                width: 100%;
                border-bottom: 1px solid var(--light);
                padding: 1rem 0;
            }

            .nav-links a {
                display: block;
                width: 100%;
                font-size: 1.1rem;
            }

            .menu-toggle {
                display: flex;
            }

            .hero-container,
            .about-content,
            .contact-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .hero {
                padding: 6rem 1.5rem 3rem;
            }

            .hero-content {
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.2rem;
            }

            .hero-content .subtitle {
                font-size: 1.1rem;
            }

            .hero-description {
                font-size: 1rem;
            }

            .cta-buttons {
                justify-content: center;
            }

            .profile-wrapper {
                width: 280px;
                height: 280px;
                margin: 0 auto;
            }

            .stats {
                justify-content: center;
            }

            .stat-number {
                font-size: 1.8rem;
            }

            .stat-number {
                font-size: 1.8rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .section-subtitle {
                font-size: 0.95rem;
            }

            .about-text p {
                font-size: 1rem;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .contact-content {
                gap: 2rem;
            }

            .hero-image {
                order: -1;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 1.8rem;
            }

            .profile-wrapper {
                width: 240px;
                height: 240px;
            }

            .avatar-head {
                width: 90px;
                height: 90px;
            }

            .avatar-body {
                width: 110px;
                height: 80px;
            }

            .btn {
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
            }

            .stats {
                gap: 1.5rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .section-title {
                font-size: 1.7rem;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        /* Enhanced Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Apply animations */
        .hero-content {
            animation: fadeInLeft 0.8s ease-out;
        }

        .hero-image {
            animation: fadeInRight 0.8s ease-out 0.2s both;
        }

        .profile-wrapper {
            animation: pulse 3s ease-in-out infinite;
        }

        .project-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .project-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .project-card:nth-child(3) {
            animation-delay: 0.2s;
        }

        .skill-category {
            animation: fadeInUp 0.6s ease-out;
        }

        .skill-category:nth-child(2) {
            animation-delay: 0.1s;
        }

        .skill-category:nth-child(3) {
            animation-delay: 0.2s;
        }

        .skill-category:nth-child(4) {
            animation-delay: 0.3s;
        }

        /* Certifications Section */
        .certifications {
            padding: 5rem 1.5rem;
            background: var(--light);
        }

        .certifications-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1.5rem;
            margin-top: 2.5rem;
        }

        .certification-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            border-left: 4px solid transparent;
            background-image: linear-gradient(var(--white), var(--white)), var(--gradient);
            background-origin: border-box;
            background-clip: padding-box, border-box;
        }

        .certification-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .cert-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .cert-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: var(--gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .cert-info h3 {
            color: var(--dark);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }

        .cert-provider {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.3rem;
        }

        .cert-year {
            color: var(--gray);
            font-size: 0.85rem;
            background: var(--light);
            padding: 0.2rem 0.8rem;
            border-radius: 12px;
            display: inline-block;
        }

        .cert-description {
            color: var(--gray);
            font-size: 0.9rem;
            line-height: 1.6;
            margin-top: 1rem;
        }

        .cert-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .cert-skill {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Animation for certifications */
        .certification-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .certification-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .certification-card:nth-child(3) {
            animation-delay: 0.2s;
        }

        .certification-card:nth-child(4) {
            animation-delay: 0.3s;
        }

        .certification-card:nth-child(5) {
            animation-delay: 0.4s;
        }

        /* Enhanced gradient backgrounds */
        .hero {
            background: linear-gradient(135deg,
                rgba(102, 126, 234, 0.1) 0%,
                rgba(118, 75, 162, 0.1) 50%,
                rgba(255, 255, 255, 0.9) 100%);
            position: relative;
            overflow: hidden;
        }

        /* Floating particles background */
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }

        /* Improved button hover effects */
        .btn-primary {
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Enhanced project cards */
        .project-image {
            position: relative;
            overflow: hidden;
        }

        .project-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-card:hover .project-image::before {
            opacity: 1;
        }

        /* Improved contact form */
        .contact-form {
            position: relative;
        }

        .contact-form::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--gradient);
            border-radius: 20px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .contact-form:hover::before {
            opacity: 0.1;
        }
        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loader {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Scroll to top button */
        .scroll-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--gradient);
            border: none;
            border-radius: 50%;
            color: var(--white);
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .scroll-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        }
    </style>
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loader"></div>
    </div>
    <!-- Navigation -->
    <nav id="navbar" role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <div class="logo" aria-label="Rohith Avula Portfolio">Rohith.dev</div>
            <ul class="nav-links" id="navLinks" role="menubar">
                <li role="none"><a href="#home" role="menuitem" aria-label="Go to Home section">Home</a></li>
                <li role="none"><a href="#about" role="menuitem" aria-label="Go to About section">About</a></li>
                <li role="none"><a href="#projects" role="menuitem" aria-label="Go to Projects section">Projects</a></li>
                <li role="none"><a href="#certifications" role="menuitem" aria-label="Go to Certifications section">Certifications</a></li>
                <li role="none"><a href="#contact" role="menuitem" aria-label="Go to Contact section">Contact</a></li>
            </ul>
            <button class="menu-toggle" id="menuToggle" aria-label="Toggle mobile menu" aria-expanded="false" aria-controls="navLinks">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <main id="main-content">
    <section class="hero" id="home" aria-label="Hero section with introduction">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Hi, I'm <span class="highlight">Rohith Avula</span></h1>
                <p class="subtitle">Full-Stack Developer & AI Engineer</p>
                <p class="hero-description">
                    I build intelligent, scalable web applications using MERN Stack and integrate cutting-edge AI solutions. Passionate about creating seamless user experiences and solving complex problems with code.
                </p>
                <div class="cta-buttons">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="https://drive.google.com/file/d/17tyl-AFk3F8xXVlpulPNtAWujrXi8-vb/view?usp=drive_link" target="_blank" class="btn btn-secondary">Download Resume</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="profile-wrapper">
                    <div class="avatar-container">
                        <div class="avatar-bg">
                            <div class="avatar-content">
                                <img src="image/profile1.jpg.jpg"
                                     alt="Rohith Avula - Full-Stack Developer & AI Engineer"
                                     class="profile-photo"
                                     loading="lazy"
                                     onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\'avatar-fallback\'>RA</div>';">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <p class="section-subtitle">Full-Stack Developer specializing in MERN + AI</p>
            <div class="about-content">
                <div class="about-text">
                    <p>
                        I'm a passionate Full-Stack Developer with expertise in building modern web applications using the MERN stack. With a B.Tech in Computer Science from Malla Reddy University (CGPA: 7.93), I've developed a strong foundation in both frontend and backend technologies.
                    </p>
                    <p>
                        What sets me apart is my expertise in integrating AI and Machine Learning into web applications. I'm currently diving deep into Prompt Engineering, Generative AI, and LLM Fine-tuning to create intelligent, context-aware applications.
                    </p>
                    <p>
                        I believe in writing clean, efficient, and scalable code while continuously learning and adapting to new technologies. My goal is to create digital solutions that not only look great but solve real-world problems.
                    </p>
                </div>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3><span class="icon">⚛️</span> Frontend</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">React.js</span>
                            <span class="skill-tag">Redux</span>
                            <span class="skill-tag">Tailwind</span>
                            <span class="skill-tag">HTML5</span>
                            <span class="skill-tag">CSS3</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h3><span class="icon">🔧</span> Backend</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">Express.js</span>
                            <span class="skill-tag">REST APIs</span>
                            <span class="skill-tag">JWT</span>
                            <span class="skill-tag">FastAPI</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h3><span class="icon">🤖</span> AI/ML</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">NLP</span>
                            <span class="skill-tag">Transformers</span>
                            <span class="skill-tag">LangChain</span>
                            <span class="skill-tag">RAG</span>
                            <span class="skill-tag">OpenAI</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h3><span class="icon">💾</span> Database</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">MySQL</span>
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Postman</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section class="projects" id="projects">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>
            <p class="section-subtitle">Real-world applications showcasing my technical skills</p>
            <div class="projects-grid">
                <!-- Project 1 -->
                <div class="project-card">
                    <div class="project-image">🛒</div>
                    <div class="project-content">
                        <h3 class="project-title">Grocer OOO</h3>
                        <p class="project-description">
                            A full-stack grocery e-commerce platform with secure authentication, shopping cart functionality, and an admin dashboard for inventory management.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                            <span class="tech-tag">JWT</span>
                        </div>
                        <div class="project-links">
                            <a href="https://grocer-ooo-full-stack-f1x9.vercel.app" target="_blank" class="project-link">
                                Live Demo →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">🎬</div>
                    <div class="project-content">
                        <h3 class="project-title">Movie Discovery App</h3>
                        <p class="project-description">
                            An interactive movie discovery platform featuring 2,000+ titles with smart pagination and lazy loading for optimal performance.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React.js</span>
                            <span class="tech-tag">REST API</span>
                            <span class="tech-tag">Netlify</span>
                        </div>
                        <div class="project-links">
                            <a href="https://moviettt.netlify.app" target="_blank" class="project-link">
                                Live Demo →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 3 -->
                <div class="project-card">
                    <div class="project-image" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">🤖</div>
                    <div class="project-content">
                        <h3 class="project-title">AI Chatbot Assistant</h3>
                        <p class="project-description">
                            An intelligent conversational AI supporting text/voice queries, PDF Q&A, academic search, and real-time web search using LangChain.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">LangChain</span>
                            <span class="tech-tag">OpenAI</span>
                            <span class="tech-tag">Streamlit</span>
                        </div>
                        <div class="project-links">
                            <a href="https://ai-chatbot-waq6iuxuxxbaetvwpxpyhp.streamlit.app" target="_blank" class="project-link">
                                Live Demo →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Certifications Section -->
    <section class="certifications" id="certifications">
        <div class="container">
            <h2 class="section-title">Certifications & Credentials</h2>
            <p class="section-subtitle">Professional certifications and continuous learning achievements</p>
            <div class="certifications-grid">
                <div class="certification-card">
                    <div class="cert-header">
                        <div class="cert-icon">🤖</div>
                        <div class="cert-info">
                            <h3>Generative AI and App Development</h3>
                            <div class="cert-provider">Udemy</div>
                            <div class="cert-year">2025</div>
                        </div>
                    </div>
                    <div class="cert-description">
                        Comprehensive course covering modern AI application development, including LLMs, prompt engineering, and AI-powered web applications.
                    </div>
                    <div class="cert-skills">
                        <span class="cert-skill">OpenAI API</span>
                        <span class="cert-skill">LangChain</span>
                        <span class="cert-skill">RAG Systems</span>
                        <span class="cert-skill">AI Integration</span>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="cert-header">
                        <div class="cert-icon">⚛️</div>
                        <div class="cert-info">
                            <h3>MERN Stack with Generative AI</h3>
                            <div class="cert-provider">TechU</div>
                            <div class="cert-year">2024</div>
                        </div>
                    </div>
                    <div class="cert-description">
                        Advanced full-stack development combining MERN technologies with AI capabilities for building intelligent web applications.
                    </div>
                    <div class="cert-skills">
                        <span class="cert-skill">React.js</span>
                        <span class="cert-skill">Node.js</span>
                        <span class="cert-skill">MongoDB</span>
                        <span class="cert-skill">AI Integration</span>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="cert-header">
                        <div class="cert-icon">📊</div>
                        <div class="cert-info">
                            <h3>Data Analysis with Python</h3>
                            <div class="cert-provider">NPTEL (IIT Roorkee)</div>
                            <div class="cert-year">2023</div>
                        </div>
                    </div>
                    <div class="cert-description">
                        Comprehensive data analysis course from prestigious IIT Roorkee, covering statistical analysis, data visualization, and machine learning fundamentals.
                    </div>
                    <div class="cert-skills">
                        <span class="cert-skill">Python</span>
                        <span class="cert-skill">Pandas</span>
                        <span class="cert-skill">NumPy</span>
                        <span class="cert-skill">Matplotlib</span>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="cert-header">
                        <div class="cert-icon">☁️</div>
                        <div class="cert-info">
                            <h3>AWS Cloud Practitioner</h3>
                            <div class="cert-provider">AWS Academy</div>
                            <div class="cert-year">2023</div>
                        </div>
                    </div>
                    <div class="cert-description">
                        Foundational AWS certification covering cloud concepts, security, architecture, pricing, and support services.
                    </div>
                    <div class="cert-skills">
                        <span class="cert-skill">AWS Services</span>
                        <span class="cert-skill">Cloud Architecture</span>
                        <span class="cert-skill">Security</span>
                        <span class="cert-skill">Cost Management</span>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="cert-header">
                        <div class="cert-icon">🐍</div>
                        <div class="cert-info">
                            <h3>Python Programming</h3>
                            <div class="cert-provider">Coursera</div>
                            <div class="cert-year">2022</div>
                        </div>
                    </div>
                    <div class="cert-description">
                        Comprehensive Python programming course covering fundamentals, object-oriented programming, and practical applications.
                    </div>
                    <div class="cert-skills">
                        <span class="cert-skill">Python Basics</span>
                        <span class="cert-skill">OOP</span>
                        <span class="cert-skill">Data Structures</span>
                        <span class="cert-skill">Algorithms</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <h2 class="section-title">Let's Work Together</h2>
            <p class="section-subtitle">I'm always open to discussing new projects and opportunities</p>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h3>Email</h3>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-details">
                            <h3>Phone</h3>
                            <p><a href="tel:+918008790864">+91-8008790864</a></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h3>Location</h3>
                            <p>Warangal, Telangana</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">💼</div>
                        <div class="contact-details">
                            <h3>LinkedIn</h3>
                            <p><a href="https://linkedin.com/in/avularohith" target="_blank">linkedin.com/in/avularohith</a></p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contactForm" novalidate>
                        <div class="form-group">
                            <label for="contactName">Name *</label>
                            <input type="text"
                                   id="contactName"
                                   name="name"
                                   placeholder="Your Name"
                                   required
                                   minlength="2"
                                   aria-describedby="nameError">
                            <div class="error-message" id="nameError">Please enter your name (at least 2 characters)</div>
                        </div>
                        <div class="form-group">
                            <label for="contactEmail">Email *</label>
                            <input type="email"
                                   id="contactEmail"
                                   name="email"
                                   placeholder="<EMAIL>"
                                   required
                                   aria-describedby="emailError">
                            <div class="error-message" id="emailError">Please enter a valid email address</div>
                        </div>
                        <div class="form-group">
                            <label for="contactMessage">Message *</label>
                            <textarea id="contactMessage"
                                      name="message"
                                      placeholder="Tell me about your project..."
                                      required
                                      minlength="10"
                                      aria-describedby="messageError"></textarea>
                            <div class="error-message" id="messageError">Please enter a message (at least 10 characters)</div>
                        </div>
                        <button type="submit" class="btn btn-primary" style="width: 100%;" id="submitBtn">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="social-links">
            <a href="https://github.com/Rohith-m36" target="_blank" class="social-link">
                <span>💻</span>
            </a>
            <a href="https://linkedin.com/in/avularohith" target="_blank" class="social-link">
                <span>💼</span>
            </a>
            <a href="mailto:<EMAIL>" class="social-link">
                <span>📧</span>
            </a>
        </div>
        <p>© 2025 Rohith Avula. Crafted with passion and code ❤️</p>
    </footer>

    <!-- Scroll to Top Button -->
    <button class="scroll-top" id="scrollTop">↑</button>

    <script>
        // Utility functions
        const $ = (selector) => document.querySelector(selector);
        const $$ = (selector) => document.querySelectorAll(selector);

        // Loading Screen
        window.addEventListener('load', () => {
            const loadingScreen = $('#loadingScreen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                }, 1000);
            }
        });

        // Mobile Menu Toggle with accessibility
        const menuToggle = $('#menuToggle');
        const navLinks = $('#navLinks');

        if (menuToggle && navLinks) {
            menuToggle.addEventListener('click', () => {
                const isActive = menuToggle.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Update ARIA attributes
                menuToggle.setAttribute('aria-expanded', isActive);
                navLinks.setAttribute('aria-hidden', !isActive);
            });

            // Close menu when clicking on a link
            $$('.nav-links a').forEach(link => {
                link.addEventListener('click', () => {
                    menuToggle.classList.remove('active');
                    navLinks.classList.remove('active');
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navLinks.setAttribute('aria-hidden', 'true');
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (menuToggle && navLinks &&
                    !menuToggle.contains(e.target) &&
                    !navLinks.contains(e.target)) {
                    menuToggle.classList.remove('active');
                    navLinks.classList.remove('active');
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navLinks.setAttribute('aria-hidden', 'true');
                }
            });

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && navLinks.classList.contains('active')) {
                    menuToggle.classList.remove('active');
                    navLinks.classList.remove('active');
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navLinks.setAttribute('aria-hidden', 'true');
                    menuToggle.focus();
                }
            });
        }

        // Optimized Scroll Handler with throttling
        let scrollTimeout;
        const scrollTopBtn = $('#scrollTop');

        function handleScroll() {
            if (scrollTimeout) return;

            scrollTimeout = setTimeout(() => {
                const scrolled = window.pageYOffset;

                // Scroll to top button
                if (scrollTopBtn) {
                    if (scrolled > 300) {
                        scrollTopBtn.classList.add('visible');
                    } else {
                        scrollTopBtn.classList.remove('visible');
                    }
                }

                // Parallax effect (only on desktop)
                if (window.innerWidth > 768) {
                    const hero = $('.hero');
                    const heroContent = $('.hero-content');
                    const heroImage = $('.hero-image');

                    if (hero && scrolled < hero.offsetHeight) {
                        if (heroContent) {
                            heroContent.style.transform = `translateY(${scrolled * 0.3}px)`;
                        }
                        if (heroImage) {
                            heroImage.style.transform = `translateY(${scrolled * 0.2}px)`;
                        }
                    }
                }

                scrollTimeout = null;
            }, 16); // ~60fps
        }

        window.addEventListener('scroll', handleScroll, { passive: true });

        if (scrollTopBtn) {
            scrollTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Enhanced Form Validation and Submission
        const contactForm = $('#contactForm');

        if (contactForm) {
            // Real-time validation
            const nameInput = $('#contactName');
            const emailInput = $('#contactEmail');
            const messageInput = $('#contactMessage');
            const submitBtn = $('#submitBtn');

            function validateField(field, validator, errorMsg) {
                const formGroup = field.closest('.form-group');
                const errorElement = formGroup.querySelector('.error-message');

                if (validator(field.value.trim())) {
                    formGroup.classList.remove('error');
                    formGroup.classList.add('success');
                    return true;
                } else {
                    formGroup.classList.remove('success');
                    formGroup.classList.add('error');
                    if (errorElement) errorElement.textContent = errorMsg;
                    return false;
                }
            }

            function validateEmail(email) {
                const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return re.test(email);
            }

            // Add real-time validation
            if (nameInput) {
                nameInput.addEventListener('blur', () => {
                    validateField(nameInput,
                        (value) => value.length >= 2,
                        'Name must be at least 2 characters long');
                });
            }

            if (emailInput) {
                emailInput.addEventListener('blur', () => {
                    validateField(emailInput,
                        validateEmail,
                        'Please enter a valid email address');
                });
            }

            if (messageInput) {
                messageInput.addEventListener('blur', () => {
                    validateField(messageInput,
                        (value) => value.length >= 10,
                        'Message must be at least 10 characters long');
                });
            }

            // Form submission
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const name = nameInput?.value.trim() || '';
                const email = emailInput?.value.trim() || '';
                const message = messageInput?.value.trim() || '';

                // Validate all fields
                const isNameValid = validateField(nameInput,
                    (value) => value.length >= 2,
                    'Name must be at least 2 characters long');
                const isEmailValid = validateField(emailInput,
                    validateEmail,
                    'Please enter a valid email address');
                const isMessageValid = validateField(messageInput,
                    (value) => value.length >= 10,
                    'Message must be at least 10 characters long');

                if (!isNameValid || !isEmailValid || !isMessageValid) {
                    // Focus on first invalid field
                    const firstError = this.querySelector('.form-group.error input, .form-group.error textarea');
                    if (firstError) firstError.focus();
                    return;
                }

                // Show loading state
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }

                // Simulate form submission
                setTimeout(() => {
                    // Success message
                    alert(`Thank you ${name}! Your message has been received. I'll get back to you at ${email} soon.`);

                    // Reset form
                    this.reset();
                    $$('.form-group').forEach(group => {
                        group.classList.remove('error', 'success');
                    });

                    // Reset button
                    if (submitBtn) {
                        submitBtn.classList.remove('loading');
                        submitBtn.disabled = false;
                    }
                }, 2000);
            });
        }

        // Smooth scroll with offset for fixed navbar
        $$('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const target = $(targetId);

                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Intersection Observer for scroll animations
        let observer;

        function initScrollAnimations() {
            if ('IntersectionObserver' in window) {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                            observer.unobserve(entry.target); // Stop observing once animated
                        }
                    });
                }, observerOptions);

                // Observe elements for scroll animations
                const animateElements = $$('.skill-category, .project-card, .contact-item');
                animateElements.forEach(el => {
                    el.style.opacity = '0';
                    el.style.transform = 'translateY(30px)';
                    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    observer.observe(el);
                });
            }
        }

        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initScrollAnimations);
        } else {
            initScrollAnimations();
        }

        // Optimized typing effect
        function typeWriter(element, text, speed = 80) {
            if (!element || !text) return;

            let i = 0;
            element.innerHTML = '';
            element.style.borderRight = '2px solid var(--primary)';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else {
                    // Remove cursor after typing is complete
                    setTimeout(() => {
                        element.style.borderRight = 'none';
                    }, 1000);
                }
            }
            type();
        }

        // Initialize typing effect after loading
        window.addEventListener('load', () => {
            setTimeout(() => {
                const subtitle = $('.hero-content .subtitle');
                if (subtitle) {
                    const originalText = subtitle.textContent;
                    typeWriter(subtitle, originalText, 80);
                }
            }, 1500);
        });

        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData && perfData.loadEventEnd - perfData.loadEventStart > 3000) {
                        console.log('Page load time is high. Consider optimizing images and scripts.');
                    }
                }, 0);
            });
        }

        // Error handling for images
        $$('img').forEach(img => {
            img.addEventListener('error', function() {
                console.warn('Failed to load image:', this.src);
                this.style.display = 'none';
            });
        });
    </script>
</body>
</html>